from decimal import Decimal

from exchange import BinanceFuturesAPI


def process_positions(futures_api):
    """
    处理已持仓的订单，进行挂单平仓
    """
    print("\n=== 第一步：处理已持仓订单 ===")

    # 查询所有持仓
    positions = futures_api.query_position_info()

    if not positions:
        print("✅ 当前没有持仓，跳过平仓步骤")
        return

    # 为每个持仓创建平仓订单
    for position in positions:
        symbol = position.get('symbol')
        position_amt = float(position.get('positionAmt', 0))
        mark_price = float(position.get('markPrice', 0))

        print(f"\n📊 处理持仓: {symbol}")
        print(f"   持仓数量: {position_amt}")
        print(f"   标记价格: {mark_price}")

        if position_amt == 0:
            continue

        try:
            # 确定平仓方向：多头持仓用SELL平仓，空头持仓用BUY平仓
            close_side = 'SELL' if position_amt > 0 else 'BUY'
            close_quantity = abs(position_amt)

            # 获取当前市价，设置一个合理的平仓价格
            order_book = futures_api.get_order_book(symbol, limit=5)
            if close_side == 'SELL':
                # 卖出平仓，使用买一价稍低的价格
                close_price = float(order_book['bids'][0][0])
                best_price = futures_api.get_bbo_price(
                    Decimal(str(close_price)), 'SELL', symbol, 5)
            else:
                # 买入平仓，使用卖一价稍高的价格
                close_price = float(order_book['asks'][0][0])
                best_price = futures_api.get_bbo_price(
                    Decimal(str(close_price)), 'BUY', symbol, 5)

            print(f"   平仓方向: {close_side}")
            print(f"   平仓数量: {close_quantity}")
            print(f"   平仓价格: {best_price}")

            # 创建平仓订单
            result = futures_api.place_close_order(
                symbol=symbol,
                side=close_side,
                quantity=close_quantity,
                price=best_price
            )

            print(f"✅ 成功创建平仓订单，订单ID: {result.get('orderId')}")

        except Exception as e:
            print(f"❌ 创建 {symbol} 平仓订单失败: {e}")


if __name__ == "__main__":
    api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
    secret_key = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

    # 初始化API客户端
    print("\n🚀 自动交易清理程序启动")
    print("=" * 50)
    print("\n1. 初始化API客户端...")
    futures_api = BinanceFuturesAPI(api_key, secret_key)
    print("✅ API客户端初始化成功")

    try:
        # 第一步：处理已持仓订单，进行挂单平仓
        process_positions(futures_api)

        # 第二步：处理未成交订单，进行取消
        # 注意：这一步会处理部分成交的订单，取消未成交部分
        # 部分成交产生的持仓在第一步中已经处理了
        futures_api.cancel_not_transaction_orders()

        print("\n" + "=" * 50)
        print("🎉 自动交易清理程序执行完成")
        print("\n📋 执行总结:")
        print("   1. ✅ 已处理所有持仓，创建了相应的平仓订单")
        print("   2. ✅ 已取消所有未成交订单")
        print("   3. ✅ 部分成交订单的已成交部分已通过平仓订单处理")

    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        print("请检查网络连接和API权限设置")
