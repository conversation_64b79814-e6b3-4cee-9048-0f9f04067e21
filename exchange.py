import json
import requests
import time
import hmac
import hashlib
import urllib.parse
import logging
from typing import Dict, Optional, Any, Union
from decimal import Decimal, ROUND_DOWN

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BinanceFuturesAPI:
    """
    Binance U本位永续合约API封装类
    用于币安U本位永续合约交易
    """

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化Binance期货API客户端

        Args:
            api_key (str): Binance API密钥
            secret_key (str): Binance API密钥对应的私钥

        Raises:
            ConnectionError: 当无法连接到币安服务器时抛出异常
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = "https://fapi.binance.com"

        # 测试服务器连通性
        self.ping_server()

        logger.info("BinanceFuturesAPI 初始化完成")

    def ping_server(self) -> None:
        """
        测试币安期货服务器连通性
        调用币安期货PING接口 /fapi/v1/ping 来测试服务器连通性

        Raises:
            ConnectionError: 当无法连接到币安服务器时抛出异常
        """
        logger.info("开始测试币安期货服务器连通性")

        try:
            # 发送PING请求到币安期货服务器
            response = requests.get(
                f'{self.base_url}/fapi/v1/ping',
                timeout=10  # 设置超时时间
            )

            # 检查响应状态
            response.raise_for_status()

            logger.info("币安期货服务器连通性测试成功")

        except requests.exceptions.RequestException as e:
            error_msg = f"无法连接到币安期货服务器: {e}"
            logger.error(error_msg)
            raise ConnectionError(error_msg) from e
        except Exception as e:
            error_msg = f"期货服务器连通性测试时发生未知错误: {e}"
            logger.error(error_msg)
            raise ConnectionError(error_msg) from e

    def generate_signature(self, query_string: str) -> str:
        """
        生成Binance API请求签名

        Args:
            query_string (str): 查询参数字符串

        Returns:
            str: HMAC SHA256签名
        """
        logger.debug("开始生成API签名")
        try:
            # 使用HMAC SHA256算法生成签名
            signature = hmac.new(
                self.secret_key.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            logger.debug("API签名生成成功")
            return signature

        except Exception as e:
            logger.error(f"生成签名时发生错误: {e}")
            raise

    def check_api_permissions(self) -> Dict[str, Any]:
        """
        检查API密钥权限和账户信息

        Returns:
            dict: 账户信息，如果成功的话

        Raises:
            Exception: 当API权限不足或密钥无效时抛出异常
        """
        logger.info("检查API密钥权限...")

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送请求获取账户信息
            response = requests.get(
                f'{self.base_url}/fapi/v2/account',
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 401:
                error_msg = "API密钥认证失败，请检查：\n" \
                    "1. API密钥和密钥是否正确\n" \
                    "2. API密钥是否有期货交易权限\n" \
                    "3. IP是否在白名单中\n" \
                    "4. 系统时间是否正确"
                logger.error(error_msg)
                raise Exception(error_msg)

            response.raise_for_status()
            result = response.json()

            logger.info("API密钥权限检查成功")
            logger.info(
                f"账户资产: {result.get('totalWalletBalance', 'N/A')} USDT")

            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"API权限检查时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"API权限检查时发生错误: {e}")
            raise

    def set_leverage(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """
        设置交易对的杠杆倍率

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            leverage (int): 杠杆倍率，1-125

        Returns:
            dict: API响应数据

        Raises:
            ValueError: 当杠杆倍率不在有效范围内时抛出
        """
        if not (1 <= leverage <= 125):
            raise ValueError(f"杠杆倍率必须在1-125之间，当前值: {leverage}")

        logger.info(f"设置 {symbol} 杠杆倍率为 {leverage}x")

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'symbol': symbol,
                'leverage': leverage,
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送请求
            response = requests.post(
                f'{self.base_url}/fapi/v1/leverage',
                headers=headers,
                params=params,
                timeout=10
            )

            response.raise_for_status()
            result = response.json()

            logger.info(f"成功设置 {symbol} 杠杆倍率为 {leverage}x")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"设置杠杆倍率时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"设置杠杆倍率时发生未知错误: {e}")
            raise

    def set_margin_type(self, symbol: str, margin_type: str) -> Dict[str, Any]:
        """
        设置交易对的仓位模式

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            margin_type (str): 仓位模式，'ISOLATED'(逐仓) 或 'CROSSED'(全仓)

        Returns:
            dict: API响应数据

        Raises:
            ValueError: 当仓位模式不是有效值时抛出
        """
        if margin_type not in ['ISOLATED', 'CROSSED']:
            raise ValueError(
                f"仓位模式必须是 'ISOLATED' 或 'CROSSED'，当前值: {margin_type}")

        margin_type_cn = "逐仓" if margin_type == "ISOLATED" else "全仓"
        logger.info(f"设置 {symbol} 仓位模式为 {margin_type_cn}")

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'symbol': symbol,
                'marginType': margin_type,
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送请求
            response = requests.post(
                f'{self.base_url}/fapi/v1/marginType',
                headers=headers,
                params=params,
                timeout=10
            )

            # 检查响应状态和错误信息
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    error_code = error_data.get('code')
                    error_msg = error_data.get('msg', '未知错误')

                    if error_code == -4046:
                        detailed_msg = f"无法设置 {symbol} 的仓位模式：没有该交易对的持仓。请先开仓后再设置仓位模式。"
                    elif error_code == -4047:
                        detailed_msg = f"无法设置 {symbol} 的仓位模式：该交易对不支持此操作或已经是目标模式。"
                    else:
                        detailed_msg = f"设置 {symbol} 仓位模式失败：{error_msg} (错误代码: {error_code})"

                    logger.error(detailed_msg)
                    raise ValueError(detailed_msg)
                except json.JSONDecodeError:
                    logger.error(f"设置仓位模式失败，无法解析错误响应: {response.text}")
                    raise ValueError(f"设置仓位模式失败: HTTP 400")

            response.raise_for_status()
            result = response.json()

            logger.info(f"成功设置 {symbol} 仓位模式为 {margin_type_cn}")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"设置仓位模式时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"设置仓位模式时发生未知错误: {e}")
            raise

    def get_order_book(self, symbol: str, limit: int = 5) -> Dict[str, Any]:
        """
        获取交易对的订单簿深度图

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            limit (int): 深度限制，默认5档

        Returns:
            dict: 订单簿数据，包含bids和asks
        """
        logger.debug(f"获取 {symbol} 订单簿深度，限制 {limit} 档")

        try:
            # 准备参数
            params = {
                'symbol': symbol,
                'limit': limit
            }

            # 发送请求
            response = requests.get(
                f'{self.base_url}/fapi/v1/depth',
                params=params,
                timeout=10
            )

            response.raise_for_status()
            result = response.json()

            logger.debug(f"成功获取 {symbol} 订单簿深度")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"获取订单簿深度时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取订单簿深度时发生未知错误: {e}")
            raise

    def get_bbo_price(self, base_price: Decimal, side: str, symbol: str, offset_ticks: int = 0) -> Decimal:
        """
        获取BBO价格（最优买卖价）

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            side (str): 订单方向，'BUY' 或 'SELL'
            offset_ticks (int): 价格偏移档位，默认0（即BBO价格）

        Returns:
            float: BBO价格

        Raises:
            ValueError: 当side不是有效值时抛出
        """

        try:
            tick_size = self._get_tick_size(symbol)
            if side == 'BUY':
                if offset_ticks > 0:
                    # 向下偏移（降低价格）
                    price = base_price - (tick_size * offset_ticks)
                else:
                    price = base_price - tick_size

            else:  # SELL
                if offset_ticks > 0:
                    # 向上偏移（提高价格）
                    tick_size = self._get_tick_size(symbol)
                    price = base_price + (tick_size * offset_ticks)
                else:
                    price = base_price + tick_size

            logger.debug(f"获取到 {symbol} {side} 方向最优价价格: {price}")
            return price

        except Exception as e:
            logger.error(f"获取最优价价格时发生错误: {e}")
            raise

    def _get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取交易对的详细信息，包括精度设置

        Args:
            symbol (str): 交易对符号

        Returns:
            dict: 交易对信息
        """
        try:
            # 获取交易规则信息
            response = requests.get(
                f'{self.base_url}/fapi/v1/exchangeInfo',
                timeout=10
            )

            response.raise_for_status()
            exchange_info = response.json()

            # 查找指定交易对的信息
            for symbol_info in exchange_info.get('symbols', []):
                if symbol_info.get('symbol') == symbol:
                    return symbol_info

            raise ValueError(f"未找到交易对 {symbol} 的信息")

        except Exception as e:
            logger.error(f"获取交易对 {symbol} 信息失败: {e}")
            raise

    def _get_quantity_precision(self, symbol: str):
        """
        获取交易对的数量精度

        Args:
            symbol (str): 交易对符号

        Returns:
            int: 数量精度（小数位数）
        """
        try:
            symbol_info = self._get_symbol_info(symbol)

            # 从filters中获取LOT_SIZE过滤器
            for filter_info in symbol_info.get('filters', []):
                if filter_info.get('filterType') == 'LOT_SIZE':
                    step_size = filter_info.get('stepSize', '1')

                    # 计算精度：stepSize为0.001则精度为3
                    if '.' in step_size:
                        precision = len(step_size.split('.')[1].rstrip('0'))
                    else:
                        precision = 0

                    logger.debug(
                        f"{symbol} 数量精度: {precision}, stepSize: {step_size}")
                    return step_size, precision

            # 如果没有找到LOT_SIZE过滤器，使用默认精度
            logger.warning(f"未找到 {symbol} 的LOT_SIZE过滤器，使用默认精度")
            return 0.1, 1

        except Exception as e:
            logger.error(f"获取 {symbol} 数量精度失败: {e}")
            # 返回默认精度
            return 0.1, 1

    def _get_min_notional(self, symbol: str) -> Decimal:
        """
        获取交易对的最小名义价值要求

        Args:
            symbol (str): 交易对符号

        Returns:
            float: 最小名义价值
        """
        try:
            symbol_info = self._get_symbol_info(symbol)

            # 从filters中获取MIN_NOTIONAL过滤器
            for filter_info in symbol_info.get('filters', []):
                if filter_info.get('filterType') == 'MIN_NOTIONAL':
                    min_notional = float(filter_info.get('notional', 5.0))
                    logger.debug(f"{symbol} 最小名义价值: {min_notional}")
                    return Decimal(str(min_notional))

            # 如果没有找到MIN_NOTIONAL过滤器，使用默认值
            logger.warning(f"未找到 {symbol} 的MIN_NOTIONAL过滤器，使用默认值5.0")
            return Decimal('5.0')

        except Exception as e:
            logger.error(f"获取 {symbol} 最小名义价值失败: {e}")
            # 返回默认值
            return Decimal('5.0')

    def _get_tick_size(self, symbol: str):
        """
        获取交易对的最小价格变动单位（内部方法）

        Args:
            symbol (str): 交易对

        Returns:
            float: 最小价格变动单位
        """
        try:
            symbol_info = self._get_symbol_info(symbol)

            # 从filters中获取PRICE_FILTER过滤器
            for filter_info in symbol_info.get('filters', []):
                if filter_info.get('filterType') == 'PRICE_FILTER':
                    tick_size_double = filter_info.get('tickSize', '0.01')
                    logger.debug(f"{symbol} 最小价格变动单位: {tick_size_double}")
                    return Decimal(tick_size_double)

            # 如果没有找到PRICE_FILTER过滤器，使用默认值
            logger.warning(f"未找到 {symbol} 的PRICE_FILTER过滤器，使用默认值0.01")
            return Decimal('0.01')

        except Exception as e:
            logger.error(f"获取 {symbol} 最小价格变动单位失败: {e}")
            # 返回默认值
            return Decimal('0.01')

    def _usdc_to_quantity(self, usdc_amount: Decimal, price: Decimal, symbol: str = "", offset_percent: int = 0):
        """
        将USDC金额转换为对应的币数量（内部方法）

        Args:
            usdc_amount (float): USDC金额
            price (float): 币的价格
            symbol (str): 交易对符号（用于精度调整）

        Returns:
            float: 对应的币数量
        """
        if price <= 0:
            raise ValueError(f"价格必须大于0，当前价格: {price}")

        if usdc_amount <= 0:
            raise ValueError(f"USDC金额必须大于0，当前金额: {usdc_amount}")

        try:
            # 获取交易对的精度信息
            step_size, quantity_precision = self._get_quantity_precision(
                symbol)
            min_notional = self._get_min_notional(symbol)

            best_price = self.get_bbo_price(
                price, 'BUY', symbol, offset_percent)

            # 计算币数量 = USDC金额 / 币价格
            quantity = (
                usdc_amount / best_price).quantize(Decimal(str(step_size)))

            # 应用精度规则
            quantity = round(quantity, quantity_precision)

            # 确保数量不为0
            if quantity == 0:
                # 如果四舍五入后为0，则设置为最小精度单位
                quantity = 10 ** (-quantity_precision)

            # 检查名义价值是否满足最小要求
            current_notional = quantity * best_price

            if current_notional < min_notional:
                logger.info(
                    f"当前名义价值 {current_notional:.4f} 小于最小要求 {min_notional}")

                # 计算满足最小名义价值的数量
                min_quantity_for_notional = min_notional / best_price
                quantity = round(min_quantity_for_notional, quantity_precision)

                # 如果四舍五入后仍然不满足最小名义价值，则向上调整
                while quantity * best_price < min_notional:
                    quantity += 10 ** (-quantity_precision)
                    quantity = round(quantity, quantity_precision)

                logger.info(f"调整数量为 {quantity} 以满足最小名义价值要求")

            # 计算最终的名义价值
            final_notional = quantity * best_price

            logger.info(f"USDC金额 {usdc_amount} 在价格 {best_price} 下转换为币数量: {quantity} "
                        f"(精度: {quantity_precision}, 名义价值: {final_notional:.4f})")

            return float(quantity), best_price

        except Exception as e:
            raise Exception(f"转换USDC金额时发生错误: {e}")

    def _validate_coin_quantity(self, coin_quantity: float, price: float, symbol: str = "") -> float:
        """
        验证和调整币数量，确保符合交易规则（内部方法）

        Args:
            coin_quantity (float): 币数量
            price (float): 币的价格
            symbol (str): 交易对符号

        Returns:
            float: 调整后的币数量
        """
        if coin_quantity <= 0:
            raise ValueError(f"币数量必须大于0，当前数量: {coin_quantity}")

        if price <= 0:
            raise ValueError(f"价格必须大于0，当前价格: {price}")

        # 应用精度规则
        if price < 10:
            quantity = round(coin_quantity, 0)  # 整数
        elif price < 100:
            quantity = round(coin_quantity, 1)  # 保留1位小数
        else:
            quantity = round(coin_quantity, 2)  # 保留2位小数

        # 确保数量至少为1
        if quantity < 1:
            quantity = 1

        # 检查名义价值是否满足最小要求（5 USDT）
        min_notional = 5.0
        current_notional = quantity * price

        if current_notional < min_notional:
            logger.warning(
                f"币数量 {quantity} 的名义价值 {current_notional:.2f} 小于最小要求 {min_notional}")
            # 自动调整到满足最小名义价值的数量
            min_quantity_for_notional = min_notional / price
            quantity = min_quantity_for_notional

            # 重新应用精度规则
            if price < 10:
                quantity = round(quantity + 0.5, 0)  # 向上取整到整数
            elif price < 100:
                quantity = round(quantity, 1)
            else:
                quantity = round(quantity, 2)

            logger.info(f"自动调整币数量为 {quantity} 以满足最小名义价值要求")

        # 计算最终的名义价值
        notional_value = quantity * price

        logger.info(
            f"币数量 {coin_quantity} 调整为 {quantity}, 名义价值: {notional_value}")

        return quantity

    def _check_position_exists(self, symbol: str) -> bool:
        """
        检查是否存在该交易对的持仓（内部方法）

        Args:
            symbol (str): 交易对

        Returns:
            bool: 是否存在持仓
        """
        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 获取持仓信息
            response = requests.get(
                f'{self.base_url}/fapi/v2/positionRisk',
                headers=headers,
                params=params,
                timeout=10
            )

            response.raise_for_status()
            positions = response.json()

            # 检查是否有该交易对的持仓
            for position in positions:
                if position.get('symbol') == symbol:
                    position_amt = float(position.get('positionAmt', 0))
                    if abs(position_amt) > 0:  # 使用绝对值检查，因为可能是负数（空头持仓）
                        logger.debug(f"{symbol} 持仓数量: {position_amt}")
                        return True

            logger.debug(f"{symbol} 无持仓")
            return False

        except Exception as e:
            logger.warning(f"检查持仓状态时发生错误: {e}")
            return False

    def _place_order(self, symbol: str, side: str, is_close: int, amount: float, price: float,
                     margin_type: str, leverage: int, amount_type: str = 'usdc', offset_percent: int = 0):
        """
        下单的通用方法（内部方法）

        Args:
            symbol (str): 交易对
            side (str): 订单方向，'BUY' 或 'SELL'
            amount (float): 数量（USDC金额或币数量）
            price (float): 价格
            margin_type (str): 仓位模式
            leverage (int): 杠杆倍率
            amount_type (str): 数量类型，'usdc' 或 'coin'

        Returns:
            dict: 订单响应数据
        """
        try:
            # 根据数量类型处理
            if amount_type == 'usdc':
                # 将USDC金额转换为币数量
                quantity, best_price = self._usdc_to_quantity(
                    Decimal(str(amount)), Decimal(str(price)), symbol, offset_percent)
                amount_desc = f"USDC金额 {amount}"
            else:  # amount_type == 'coin'
                best_price = self.get_bbo_price(
                    Decimal(str(price)), 'SELL', symbol, offset_percent)
                # 直接使用币数量，但需要验证精度和最小名义价值
                quantity = amount

            # 先设置杠杆
            self.set_leverage(symbol, leverage)

            # 准备下单参数
            timestamp = int(time.time() * 1000)

            if is_close == 0:
                params = {
                    'symbol': symbol,
                    'side': side,
                    'type': 'LIMIT',
                    'timeInForce': 'GTC',  # 有效期，一直有效
                    'quantity': str(quantity),
                    'price': str(best_price),
                    'positionSide': 'LONG' if side == 'BUY' else 'SHORT',  # 指定持仓方向
                    'timestamp': timestamp
                }

            elif is_close == 1:
                params = {
                    'symbol': symbol,
                    'side': side,
                    'type': 'LIMIT',
                    'timeInForce': 'GTC',  # 有效期，一直有效
                    'price': str(best_price),
                    'quantity': str(quantity),
                    'positionSide': 'LONG' if side == 'SELL' else 'SHORT',  # 平仓时方向相反
                    'timestamp': timestamp
                }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送下单请求
            response = requests.post(
                f'{self.base_url}/fapi/v1/order',
                headers=headers,
                params=params,
                timeout=10
            )

            return response

        except Exception as e:
            logger.error(f"下单时发生错误: {e}")
            raise

    def get_order_info(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """
        根据订单号查看订单详情信息

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            order_id (int): 订单ID

        Returns:
            dict: 订单详情信息

        Raises:
            ValueError: 当订单ID无效时抛出
            Exception: 当查询失败时抛出异常
        """
        if not order_id or order_id <= 0:
            raise ValueError(f"订单ID必须是正整数，当前值: {order_id}")

        logger.info(f"查询 {symbol} 订单详情，订单ID: {order_id}")

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'symbol': symbol,
                'orderId': order_id,
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送请求
            response = requests.get(
                f'{self.base_url}/fapi/v1/order',
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 400:
                try:
                    error_data = response.json()
                    error_code = error_data.get('code')
                    error_msg = error_data.get('msg', '未知错误')

                    if error_code == -2013:
                        detailed_msg = f"订单不存在：订单ID {order_id} 在交易对 {symbol} 中未找到"
                    else:
                        detailed_msg = f"查询订单失败：{error_msg} (错误代码: {error_code})"

                    logger.error(detailed_msg)
                    raise ValueError(detailed_msg)
                except json.JSONDecodeError:
                    logger.error(f"查询订单失败，无法解析错误响应: {response.text}")
                    raise ValueError(f"查询订单失败: HTTP 400")

            response.raise_for_status()
            result = response.json()

            # 格式化订单状态信息
            order_status = result.get('status', 'UNKNOWN')
            order_side = result.get('side', 'UNKNOWN')
            order_type = result.get('type', 'UNKNOWN')
            orig_qty = result.get('origQty', '0')
            executed_qty = result.get('executedQty', '0')
            price = result.get('price', '0')
            avg_price = result.get('avgPrice', '0')

            logger.info(f"订单详情查询成功 - 状态: {order_status}, 方向: {order_side}, "
                        f"委托量: {orig_qty}, 成交量: {executed_qty}, 委托价: {price}")

            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"查询订单详情时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询订单详情时发生未知错误: {e}")
            raise

    def query_position_info(self, symbol=None):
        """
        查询已持仓的合约订单信息

        Args:
            futures_api: BinanceFuturesAPI实例
            symbol: 可选，指定查询的交易对，如果不指定则查询所有持仓
        """
        print(f"\n2. 查询持仓信息...")

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 获取持仓信息
            response = requests.get(
                f'{self.base_url}/fapi/v2/positionRisk',
                headers=headers,
                params=params,
                timeout=10
            )

            response.raise_for_status()
            positions = response.json()

            # 过滤有持仓的合约
            active_positions = []
            for position in positions:
                position_amt = float(position.get('positionAmt', 0))
                if abs(position_amt) > 0:  # 有持仓
                    active_positions.append(position)

            if not active_positions:
                print("❌ 当前没有任何持仓")
                return []

            print(f"✅ 找到 {len(active_positions)} 个持仓合约:")

            return active_positions

        except Exception as e:
            print(f"❌ 查询持仓信息失败: {e}")
            logger.error(f"查询持仓信息失败: {e}")
            return []

    def query_open_orders(self, symbol: str = None) -> list:
        """
        查询当前未成交的订单

        Args:
            symbol (str, optional): 交易对，如 'BTCUSDT'。如果不指定则查询所有交易对

        Returns:
            list: 未成交订单列表

        Raises:
            Exception: 当查询失败时抛出异常
        """
        logger.info(f"查询未成交订单" + (f" - {symbol}" if symbol else " - 所有交易对"))

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'timestamp': timestamp
            }

            # 如果指定了交易对，添加到参数中
            if symbol:
                params['symbol'] = symbol

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送请求
            response = requests.get(
                f'{self.base_url}/fapi/v1/openOrders',
                headers=headers,
                params=params,
                timeout=10
            )

            response.raise_for_status()
            orders = response.json()

            logger.info(f"查询到 {len(orders)} 个未成交订单")
            return orders

        except requests.exceptions.RequestException as e:
            logger.error(f"查询未成交订单时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询未成交订单时发生未知错误: {e}")
            raise

    def cancel_order(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """
        取消指定的订单

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            order_id (int): 订单ID

        Returns:
            dict: 取消订单的响应数据

        Raises:
            ValueError: 当订单ID无效时抛出
            Exception: 当取消失败时抛出异常
        """
        if not order_id or order_id <= 0:
            raise ValueError(f"订单ID必须是正整数，当前值: {order_id}")

        logger.info(f"取消 {symbol} 订单，订单ID: {order_id}")

        try:
            # 准备参数
            timestamp = int(time.time() * 1000)
            params = {
                'symbol': symbol,
                'orderId': order_id,
                'timestamp': timestamp
            }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送取消订单请求
            response = requests.delete(
                f'{self.base_url}/fapi/v1/order',
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 400:
                try:
                    error_data = response.json()
                    error_code = error_data.get('code')
                    error_msg = error_data.get('msg', '未知错误')

                    if error_code == -2011:
                        detailed_msg = f"订单不存在或已取消：订单ID {order_id} 在交易对 {symbol} 中未找到"
                    else:
                        detailed_msg = f"取消订单失败：{error_msg} (错误代码: {error_code})"

                    logger.error(detailed_msg)
                    raise ValueError(detailed_msg)
                except json.JSONDecodeError:
                    logger.error(f"取消订单失败，无法解析错误响应: {response.text}")
                    raise ValueError(f"取消订单失败: HTTP 400")

            response.raise_for_status()
            result = response.json()

            logger.info(f"成功取消订单 {order_id}")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"取消订单时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"取消订单时发生未知错误: {e}")
            raise

    def place_close_order(self, symbol: str, side: str, quantity: float, price: float = None) -> Dict[str, Any]:
        """
        创建平仓订单

        Args:
            symbol (str): 交易对，如 'BTCUSDT'
            side (str): 平仓方向，'BUY' 或 'SELL'
            quantity (float): 平仓数量
            price (float, optional): 平仓价格，如果不指定则使用市价单

        Returns:
            dict: 订单响应数据

        Raises:
            ValueError: 当参数无效时抛出
            Exception: 当下单失败时抛出异常
        """
        if quantity <= 0:
            raise ValueError(f"平仓数量必须大于0，当前值: {quantity}")

        if side not in ['BUY', 'SELL']:
            raise ValueError(f"平仓方向必须是 'BUY' 或 'SELL'，当前值: {side}")

        logger.info(f"创建 {symbol} 平仓订单 - 方向: {side}, 数量: {quantity}")

        try:
            # 准备下单参数
            timestamp = int(time.time() * 1000)

            if price is None:
                # 市价单
                params = {
                    'symbol': symbol,
                    'side': side,
                    'type': 'MARKET',
                    'quantity': str(quantity),
                    'reduceOnly': 'true',  # 只减仓，用于平仓
                    'timestamp': timestamp
                }
            else:
                # 限价单
                params = {
                    'symbol': symbol,
                    'side': side,
                    'type': 'LIMIT',
                    'timeInForce': 'GTC',
                    'quantity': str(quantity),
                    'price': str(price),
                    'reduceOnly': 'true',  # 只减仓，用于平仓
                    'timestamp': timestamp
                }

            # 生成查询字符串和签名
            query_string = urllib.parse.urlencode(params)
            signature = self.generate_signature(query_string)
            params['signature'] = signature

            # 准备请求头
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 发送下单请求
            response = requests.post(
                f'{self.base_url}/fapi/v1/order',
                headers=headers,
                params=params,
                timeout=10
            )

            response.raise_for_status()
            result = response.json()

            logger.info(f"成功创建平仓订单，订单ID: {result.get('orderId')}")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"创建平仓订单时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"创建平仓订单时发生未知错误: {e}")
            raise

    def cancel_not_transaction_orders(self):
        """
        处理未成交的订单，进行取消
        """
        print("\n=== 第二步：处理未成交订单 ===")

        try:
            # 查询所有未成交订单
            open_orders = self.query_open_orders()

            if not open_orders:
                print("✅ 当前没有未成交订单，跳过取消步骤")
                return

            print(f"📊 找到 {len(open_orders)} 个未成交订单")

            # 逐个取消未成交订单
            for order in open_orders:
                symbol = order.get('symbol')
                order_id = order.get('orderId')
                side = order.get('side')
                orig_qty = order.get('origQty')
                executed_qty = order.get('executedQty')
                status = order.get('status')

                print(f"\n📋 处理订单: {symbol} - {order_id}")
                print(f"   方向: {side}")
                print(f"   委托数量: {orig_qty}")
                print(f"   成交数量: {executed_qty}")
                print(f"   订单状态: {status}")

                try:
                    # 取消订单
                    self.cancel_order(symbol, order_id)
                    print(f"✅ 成功取消订单 {order_id}")

                    # 检查是否有部分成交
                    executed_qty_float = float(executed_qty)
                    if executed_qty_float > 0:
                        print(
                            f"⚠️  订单 {order_id} 有部分成交数量: {executed_qty_float}")
                        print("   部分成交产生的持仓将在第一步的持仓处理中自动平仓")

                except Exception as e:
                    print(f"❌ 取消订单 {order_id} 失败: {e}")

        except Exception as e:
            print(f"❌ 查询未成交订单失败: {e}")

    def place_buy_order_test(self, symbol: str, usdc_amount: float, margin_type: str = 'CROSSED',
                             leverage: int = 2, offset_percent: int = 5) -> Dict[str, Any]:
        """
        执行买入下单测试（基于test_buy_exchange.py封装）

        Args:
            symbol (str): 交易对，如 'IPUSDC'
            usdc_amount (float): 下单金额 USDC
            margin_type (str): 仓位模式，默认 'CROSSED'
            leverage (int): 杠杆倍率，默认 2
            offset_percent (int): 价格滑点，默认 5

        Returns:
            dict: 下单结果
        """
        logger.info(f"开始执行买入下单测试 - {symbol}")

        # 设置杠杆倍率
        print(f"\n设置 {symbol} 杠杆倍率为 {leverage}x...")
        self.set_leverage(symbol, leverage)
        print(f"   成功设置 {symbol} 杠杆倍率为 {leverage}x")

        # 获取订单信息并下单
        while True:
            print(f"\n获取 {symbol} 订单信息...")
            try:
                order_book = self.get_order_book(symbol, limit=10)
                if order_book.get('bids') and order_book.get('asks'):
                    bid_price = float(order_book['bids'][0][0])
                    ask_price = float(order_book['asks'][0][0])
                    print(
                        f"✅ 买一价: {bid_price}, 卖一价: {ask_price}, 设置标记价格为买一价...")
                else:
                    print("❌ 获取订单信息异常")
                    raise ValueError("获取订单信息异常")
            except Exception as e:
                print(f"❌ 获取订单信息失败: {e}")
                raise ValueError(f"获取订单信息失败: {e}")

            # 执行下单
            print(f"\n执行下单...")
            response = self._place_order(
                symbol, 'BUY', 0, usdc_amount, bid_price, margin_type, leverage, 'usdc', offset_percent)
            result = response.json()

            if response.status_code == 400:
                result_code = result.get('code')
                if result_code == -2021:
                    print("❌ 订单会立即成交并成为taker，无法挂单")
                    print("开始拉取最新价重试...")
                    time.sleep(1)
                    continue
                else:
                    error_code = result.get('code')
                    error_msg = result.get('msg', '未知错误')
                    detailed_msg = f"下单失败：{error_msg} (错误代码: {error_code})"
                    logger.error(f"下单失败: HTTP 400, 错误信息: {detailed_msg}")
                    raise Exception(detailed_msg)

            elif response.status_code == 200:
                print("🎉 下单成功！")
                print(f"   订单ID: {result.get('orderId')}")
                print(f"   订单状态: {result.get('status')}")
                print(f"   成交数量: {result.get('executedQty', 0)}")
                print(f"   委托数量: {result.get('origQty')}")
                print(f"   委托价格: {result.get('price')}")
                return result
            else:
                error_msg = f"下单失败: 无法解析错误响应: {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)

    def place_sell_order_test(self, symbol: str, order_id: int, margin_type: str = 'CROSSED',
                              leverage: int = 2, offset_percent: int = 5) -> Dict[str, Any]:
        """
        执行卖出平仓下单测试（基于test_sell_exchange.py封装）

        Args:
            symbol (str): 交易对，如 'IPUSDC'
            order_id (int): 需要平仓的订单ID
            margin_type (str): 仓位模式，默认 'CROSSED'
            leverage (int): 杠杆倍率，默认 2
            offset_percent (int): 价格滑点，默认 5

        Returns:
            dict: 下单结果
        """
        logger.info(f"开始执行卖出平仓下单测试 - {symbol}")

        # 查询需要平仓的订单
        order_info = self.get_order_info(symbol, order_id)
        print(f"订单状态: {order_info['status']}")
        print(f"订单方向: {order_info['side']}")
        print(f"委托数量: {order_info['origQty']}")
        print(f"成交数量: {order_info['executedQty']}")

        # 平多仓的数量
        usdc_amount = order_info['executedQty']
        is_close = 1

        # 设置杠杆倍率
        print(f"\n设置 {symbol} 杠杆倍率为 {leverage}x...")
        self.set_leverage(symbol, leverage)
        print(f"   成功设置 {symbol} 杠杆倍率为 {leverage}x")

        # 获取订单信息并下单
        while True:
            print(f"\n获取 {symbol} 订单信息...")
            try:
                order_book = self.get_order_book(symbol, limit=10)
                if order_book.get('bids') and order_book.get('asks'):
                    bid_price = float(order_book['bids'][0][0])
                    ask_price = float(order_book['asks'][0][0])
                    print(
                        f"✅ 买一价: {bid_price}, 卖一价: {ask_price}, 设置标记价格为卖一价...")
                else:
                    print("❌ 获取订单信息异常")
                    raise ValueError("获取订单信息异常")
            except Exception as e:
                print(f"❌ 获取订单信息失败: {e}")
                raise ValueError(f"获取订单信息失败: {e}")

            # 执行下单
            print(f"\n执行下单...")
            response = self._place_order(
                symbol, 'SELL', is_close, usdc_amount, ask_price, margin_type, leverage, 'coin', offset_percent)
            result = response.json()

            if response.status_code == 400:
                result_code = result.get('code')
                if result_code == -2021:
                    print("❌ 订单会立即成交并成为taker，无法挂单")
                    print("开始拉取最新价重试...")
                    time.sleep(1)
                    continue
                else:
                    error_code = result.get('code')
                    error_msg = result.get('msg', '未知错误')
                    detailed_msg = f"下单失败：{error_msg} (错误代码: {error_code})"
                    logger.error(f"下单失败: HTTP 400, 错误信息: {detailed_msg}")
                    raise Exception(detailed_msg)

            elif response.status_code == 200:
                print("🎉 下单成功！")
                print(f"   订单ID: {result.get('orderId')}")
                print(f"   订单状态: {result.get('status')}")
                print(f"   成交数量: {result.get('executedQty', 0)}")
                print(f"   委托数量: {result.get('origQty')}")
                print(f"   委托价格: {result.get('price')}")
                return result
            else:
                error_msg = f"下单失败: 无法解析错误响应: {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
