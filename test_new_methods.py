#!/usr/bin/env python3
"""
测试新添加的方法
"""

from exchange import BinanceFuturesAPI

def test_new_methods():
    """测试新添加的方法"""
    
    api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
    secret_key = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

    # 初始化API客户端
    print("\n🧪 测试新添加的方法")
    print("=" * 40)
    
    try:
        futures_api = BinanceFuturesAPI(api_key, secret_key)
        print("✅ API客户端初始化成功")
        
        # 测试查询未成交订单方法
        print("\n1. 测试查询未成交订单...")
        try:
            open_orders = futures_api.query_open_orders()
            print(f"✅ 查询未成交订单成功，找到 {len(open_orders)} 个订单")
            
            if open_orders:
                print("📋 未成交订单列表:")
                for order in open_orders[:3]:  # 只显示前3个
                    print(f"   - {order.get('symbol')} | 订单ID: {order.get('orderId')} | "
                          f"方向: {order.get('side')} | 状态: {order.get('status')}")
            else:
                print("   当前没有未成交订单")
                
        except Exception as e:
            print(f"❌ 查询未成交订单失败: {e}")
        
        # 测试查询持仓信息
        print("\n2. 测试查询持仓信息...")
        try:
            positions = futures_api.query_position_info()
            print(f"✅ 查询持仓信息成功，找到 {len(positions)} 个持仓")
            
            if positions:
                print("📊 持仓列表:")
                for pos in positions[:3]:  # 只显示前3个
                    print(f"   - {pos.get('symbol')} | 数量: {pos.get('positionAmt')} | "
                          f"标记价格: {pos.get('markPrice')}")
            else:
                print("   当前没有持仓")
                
        except Exception as e:
            print(f"❌ 查询持仓信息失败: {e}")
        
        print("\n" + "=" * 40)
        print("🎉 方法测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_new_methods()
